"""
数据源适配器模块

定义统一的数据源适配器接口，支持不同类型的数据源（RSS、API、Web等）
通过适配器模式实现数据源的统一管理和数据格式标准化。
"""

import time
import hashlib
import requests
import feedparser
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from loguru import logger

from .models import DataSourceConfig, UnifiedNewsData, DataSourceStatus


class DataSourceAdapter(ABC):
    """数据源适配器基类"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化数据源适配器
        
        Args:
            config: 数据源配置
        """
        self.config = config
        self.status = DataSourceStatus(
            source_id=config.source_id,
            status="inactive"
        )
        self._session = requests.Session()
        self._setup_session()
        
    def _setup_session(self):
        """设置HTTP会话"""
        headers = self.config.config.get('headers', {})
        headers.setdefault('User-Agent', 
                          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        self._session.headers.update(headers)
        
        # 设置超时
        self._session.timeout = self.config.timeout
        
    @abstractmethod
    def fetch_data(self) -> List[UnifiedNewsData]:
        """
        获取数据的抽象方法
        
        Returns:
            统一格式的新闻数据列表
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """
        验证配置的抽象方法
        
        Returns:
            配置是否有效
        """
        pass
    
    def fetch_with_retry(self) -> List[UnifiedNewsData]:
        """
        带重试机制的数据获取
        
        Returns:
            统一格式的新闻数据列表
        """
        last_error = None
        
        for attempt in range(self.config.retry_count + 1):
            try:
                start_time = time.time()
                
                # 更新状态
                self.status.status = "active"
                self.status.last_check_time = datetime.now()
                
                # 获取数据
                data = self.fetch_data()
                
                # 记录成功信息
                response_time = time.time() - start_time
                self.status.response_time = response_time
                self.status.success_count += 1
                self.status.total_fetched += len(data)
                self.status.last_success_time = datetime.now()
                self.status.error_count = 0  # 重置错误计数
                self.status.last_error = None
                
                logger.info(f"数据源 {self.config.source_id} 获取成功: {len(data)} 条数据, "
                           f"耗时 {response_time:.2f}s")
                
                return data
                
            except Exception as e:
                last_error = str(e)
                self.status.error_count += 1
                self.status.last_error = last_error
                
                if attempt < self.config.retry_count:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"数据源 {self.config.source_id} 获取失败 (尝试 {attempt + 1}): "
                                 f"{last_error}, {wait_time}s 后重试")
                    time.sleep(wait_time)
                else:
                    logger.error(f"数据源 {self.config.source_id} 获取失败，已达最大重试次数: {last_error}")
        
        # 所有重试都失败
        self.status.status = "error"
        return []
    
    def generate_news_id(self, title: str, url: str = "") -> str:
        """
        生成新闻唯一标识
        
        Args:
            title: 新闻标题
            url: 新闻链接
            
        Returns:
            新闻唯一标识
        """
        content = f"{self.config.source_id}_{title}_{url}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def parse_datetime(self, time_str: Union[str, datetime, None]) -> Optional[datetime]:
        """
        解析时间字符串
        
        Args:
            time_str: 时间字符串或datetime对象
            
        Returns:
            解析后的datetime对象
        """
        if not time_str:
            return None
            
        if isinstance(time_str, datetime):
            return time_str
            
        if not isinstance(time_str, str):
            return None
            
        # 尝试多种时间格式
        time_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y-%m-%d',
            '%a, %d %b %Y %H:%M:%S %Z',
            '%a, %d %b %Y %H:%M:%S %z'
        ]
        
        for fmt in time_formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        
        # 尝试使用dateutil解析
        try:
            from dateutil import parser
            return parser.parse(time_str)
        except:
            logger.warning(f"无法解析时间字符串: {time_str}")
            return None
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 文本内容
            max_keywords: 最大关键词数量
            
        Returns:
            关键词列表
        """
        if not text:
            return []
            
        # 简单的关键词提取逻辑
        # 在实际应用中可以使用更复杂的NLP技术
        import re
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 提取中文词汇（简单实现）
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
        
        # 统计词频
        word_count = {}
        for word in chinese_words:
            if len(word) >= 2:  # 只考虑长度>=2的词
                word_count[word] = word_count.get(word, 0) + 1
        
        # 按频率排序并返回前N个
        sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
        return [word for word, count in sorted_words[:max_keywords]]
    
    def get_status(self) -> DataSourceStatus:
        """
        获取数据源状态
        
        Returns:
            数据源状态
        """
        return self.status
    
    def is_healthy(self) -> bool:
        """
        检查数据源是否健康
        
        Returns:
            是否健康
        """
        if not self.config.enabled:
            return False
            
        # 如果错误次数过多，认为不健康
        if self.status.error_count >= 5:
            return False
            
        # 如果长时间没有成功获取数据，认为不健康
        if self.status.last_success_time:
            time_since_success = datetime.now() - self.status.last_success_time
            if time_since_success > timedelta(hours=24):
                return False
        
        return True
    
    def reset_status(self):
        """重置状态"""
        self.status.error_count = 0
        self.status.last_error = None
        self.status.status = "inactive"


class RSSAdapter(DataSourceAdapter):
    """RSS数据源适配器"""
    
    def validate_config(self) -> bool:
        """验证RSS配置"""
        return bool(self.config.config.get('url'))
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从RSS源获取数据"""
        url = self.config.config.get('url')
        if not url:
            raise ValueError("RSS URL未配置")
        
        # 解析RSS
        feed = feedparser.parse(url)
        
        if feed.bozo:
            logger.warning(f"RSS解析警告: {feed.bozo_exception}")
        
        news_list = []
        current_time = datetime.now()
        
        for entry in feed.entries:
            try:
                # 生成新闻ID
                news_id = self.generate_news_id(
                    entry.title, 
                    getattr(entry, 'link', '')
                )
                
                # 解析发布时间
                publish_time = None
                if hasattr(entry, 'published_parsed') and entry.published_parsed:
                    publish_time = datetime(*entry.published_parsed[:6])
                elif hasattr(entry, 'published'):
                    publish_time = self.parse_datetime(entry.published)
                
                # 提取内容
                content = getattr(entry, 'description', '') or getattr(entry, 'summary', '')
                
                # 提取关键词
                keywords = self.extract_keywords(f"{entry.title} {content}")
                
                # 创建统一数据结构
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=entry.title,
                    content=content,
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=getattr(entry, 'link', None),
                    publish_time=publish_time,
                    fetch_time=current_time,
                    keywords=keywords,
                    extra_data={
                        'rss_entry': {
                            'author': getattr(entry, 'author', None),
                            'category': getattr(entry, 'category', None),
                            'guid': getattr(entry, 'guid', None)
                        }
                    }
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析RSS条目失败: {str(e)}")
                continue
        
        return news_list


class APIAdapter(DataSourceAdapter):
    """API数据源适配器"""
    
    def validate_config(self) -> bool:
        """验证API配置"""
        return bool(self.config.config.get('url'))
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从API获取数据"""
        url = self.config.config.get('url')
        if not url:
            raise ValueError("API URL未配置")
        
        # 准备请求参数
        headers = self.config.config.get('headers', {})
        params = self.config.config.get('params', {})
        
        # 添加API密钥
        api_key = self.config.config.get('api_key')
        if api_key:
            auth_header = self.config.config.get('auth_header', 'Authorization')
            auth_format = self.config.config.get('auth_format', 'Bearer {}')
            headers[auth_header] = auth_format.format(api_key)
        
        # 发送请求
        response = self._session.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        data = response.json()
        return self._parse_api_response(data)
    
    def _parse_api_response(self, data: Dict[str, Any]) -> List[UnifiedNewsData]:
        """解析API响应数据"""
        news_list = []
        current_time = datetime.now()
        
        # 获取数据项路径配置
        data_path = self.config.config.get('data_path', ['data', 'items', 'results'])
        items = data
        
        # 按路径提取数据项
        for path in data_path:
            if isinstance(items, dict) and path in items:
                items = items[path]
                break
            elif isinstance(items, list):
                break
        
        if not isinstance(items, list):
            items = [items] if items else []
        
        # 字段映射配置
        field_mapping = self.config.config.get('field_mapping', {
            'title': ['title', 'headline', 'name'],
            'content': ['content', 'description', 'summary', 'body'],
            'url': ['url', 'link', 'href'],
            'publish_time': ['publish_time', 'created_at', 'date', 'timestamp'],
            'category': ['category', 'type', 'section'],
            'author': ['author', 'source', 'publisher']
        })
        
        for item in items:
            try:
                # 提取标题
                title = self._extract_field_value(item, field_mapping.get('title', ['title']))
                if not title:
                    continue
                
                # 生成新闻ID
                url = self._extract_field_value(item, field_mapping.get('url', ['url']))
                news_id = self.generate_news_id(title, url or '')
                
                # 提取其他字段
                content = self._extract_field_value(item, field_mapping.get('content', ['content']))
                publish_time_str = self._extract_field_value(item, field_mapping.get('publish_time', ['publish_time']))
                publish_time = self.parse_datetime(publish_time_str)
                category = self._extract_field_value(item, field_mapping.get('category', ['category']))
                
                # 提取关键词
                keywords = self.extract_keywords(f"{title} {content or ''}")
                
                # 创建统一数据结构
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=content,
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=url,
                    publish_time=publish_time,
                    fetch_time=current_time,
                    category=category,
                    keywords=keywords,
                    view_count=self._extract_field_value(item, ['view_count', 'views']),
                    comment_count=self._extract_field_value(item, ['comment_count', 'comments']),
                    share_count=self._extract_field_value(item, ['share_count', 'shares']),
                    like_count=self._extract_field_value(item, ['like_count', 'likes']),
                    extra_data={'api_item': item}
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析API条目失败: {str(e)}")
                continue
        
        return news_list
    
    def _extract_field_value(self, item: Dict[str, Any], field_names: List[str]) -> Any:
        """从数据项中提取字段值"""
        for field_name in field_names:
            if field_name in item:
                return item[field_name]
        return None


class WebAdapter(DataSourceAdapter):
    """Web爬虫数据源适配器"""

    def validate_config(self) -> bool:
        """验证Web配置"""
        return bool(self.config.config.get('url'))

    def fetch_data(self) -> List[UnifiedNewsData]:
        """从网页爬取数据"""
        url = self.config.config.get('url')
        if not url:
            raise ValueError("Web URL未配置")

        # 获取网页内容
        response = self._session.get(url)
        response.raise_for_status()

        # 解析HTML
        try:
            from bs4 import BeautifulSoup
        except ImportError:
            raise ImportError("需要安装 beautifulsoup4: pip install beautifulsoup4")

        soup = BeautifulSoup(response.content, 'html.parser')
        return self._parse_html_content(soup)

    def _parse_html_content(self, soup) -> List[UnifiedNewsData]:
        """解析HTML内容"""
        news_list = []
        current_time = datetime.now()

        # 获取选择器配置
        selectors = self.config.config.get('selectors', {})
        container_selector = selectors.get('container', 'article, .news-item, .post')
        title_selector = selectors.get('title', 'h1, h2, h3, .title')
        content_selector = selectors.get('content', '.content, .body, p')
        link_selector = selectors.get('link', 'a')
        time_selector = selectors.get('time', '.time, .date, time')

        # 查找新闻容器
        containers = soup.select(container_selector)

        for container in containers:
            try:
                # 提取标题
                title_elem = container.select_one(title_selector)
                if not title_elem:
                    continue
                title = title_elem.get_text(strip=True)

                # 提取链接
                link_elem = container.select_one(link_selector)
                url = None
                if link_elem and link_elem.get('href'):
                    url = link_elem['href']
                    # 处理相对链接
                    if url.startswith('/'):
                        base_url = self.config.config.get('url')
                        from urllib.parse import urljoin
                        url = urljoin(base_url, url)

                # 生成新闻ID
                news_id = self.generate_news_id(title, url or '')

                # 提取内容
                content_elems = container.select(content_selector)
                content = ' '.join([elem.get_text(strip=True) for elem in content_elems])

                # 提取时间
                time_elem = container.select_one(time_selector)
                publish_time = None
                if time_elem:
                    time_text = time_elem.get_text(strip=True)
                    publish_time = self.parse_datetime(time_text)

                # 提取关键词
                keywords = self.extract_keywords(f"{title} {content}")

                # 创建统一数据结构
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=content,
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=url,
                    publish_time=publish_time,
                    fetch_time=current_time,
                    keywords=keywords,
                    extra_data={
                        'html_container': str(container)[:500]  # 保存部分HTML用于调试
                    }
                )

                news_list.append(news_data)

            except Exception as e:
                logger.warning(f"解析HTML条目失败: {str(e)}")
                continue

        return news_list


class CustomAdapter(DataSourceAdapter):
    """自定义数据源适配器"""

    def validate_config(self) -> bool:
        """验证自定义配置"""
        # 自定义适配器需要提供处理函数
        return bool(self.config.config.get('handler_function'))

    def fetch_data(self) -> List[UnifiedNewsData]:
        """使用自定义处理函数获取数据"""
        handler_name = self.config.config.get('handler_function')
        if not handler_name:
            raise ValueError("自定义处理函数未配置")

        # 动态导入处理函数
        try:
            module_name, function_name = handler_name.rsplit('.', 1)
            module = __import__(module_name, fromlist=[function_name])
            handler_function = getattr(module, function_name)
        except (ImportError, AttributeError) as e:
            raise ValueError(f"无法导入处理函数 {handler_name}: {str(e)}")

        # 调用处理函数
        try:
            raw_data = handler_function(self.config.config)
            return self._convert_to_unified_format(raw_data)
        except Exception as e:
            raise ValueError(f"自定义处理函数执行失败: {str(e)}")

    def _convert_to_unified_format(self, raw_data: List[Dict[str, Any]]) -> List[UnifiedNewsData]:
        """将原始数据转换为统一格式"""
        news_list = []
        current_time = datetime.now()

        for item in raw_data:
            try:
                title = item.get('title')
                if not title:
                    continue

                # 生成新闻ID
                url = item.get('url', '')
                news_id = self.generate_news_id(title, url)

                # 解析发布时间
                publish_time = self.parse_datetime(item.get('publish_time'))

                # 提取关键词
                content = item.get('content', '')
                keywords = self.extract_keywords(f"{title} {content}")

                # 创建统一数据结构
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=content,
                    summary=item.get('summary'),
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=url,
                    publish_time=publish_time,
                    fetch_time=current_time,
                    image_urls=item.get('image_urls', []),
                    video_urls=item.get('video_urls', []),
                    category=item.get('category'),
                    tags=item.get('tags', []),
                    keywords=keywords,
                    view_count=item.get('view_count'),
                    comment_count=item.get('comment_count'),
                    share_count=item.get('share_count'),
                    like_count=item.get('like_count'),
                    extra_data=item.get('extra_data', {})
                )

                news_list.append(news_data)

            except Exception as e:
                logger.warning(f"转换自定义数据失败: {str(e)}")
                continue

        return news_list


# 适配器注册表
ADAPTER_REGISTRY = {
    'rss': RSSAdapter,
    'api': APIAdapter,
    'web': WebAdapter,
    'custom': CustomAdapter
}


def get_adapter_class(adapter_type: str) -> type:
    """
    获取适配器类

    Args:
        adapter_type: 适配器类型

    Returns:
        适配器类
    """
    if adapter_type not in ADAPTER_REGISTRY:
        raise ValueError(f"不支持的适配器类型: {adapter_type}")

    return ADAPTER_REGISTRY[adapter_type]


def register_adapter(adapter_type: str, adapter_class: type):
    """
    注册新的适配器类型

    Args:
        adapter_type: 适配器类型名称
        adapter_class: 适配器类
    """
    if not issubclass(adapter_class, DataSourceAdapter):
        raise ValueError("适配器类必须继承自 DataSourceAdapter")

    ADAPTER_REGISTRY[adapter_type] = adapter_class
    logger.info(f"注册适配器类型: {adapter_type}")


def list_adapter_types() -> List[str]:
    """
    列出所有可用的适配器类型

    Returns:
        适配器类型列表
    """
    return list(ADAPTER_REGISTRY.keys())


# 注册预定义适配器
def _register_predefined_adapters():
    """注册预定义的适配器类"""
    try:
        from .predefined_adapters import (
            TencentFinanceAdapter,
            XueqiuAdapter,
            BaiduHotAdapter,
            WeiboHotAdapter,
            JinrongjieBaiduAdapter
        )

        # 注册特殊适配器
        ADAPTER_REGISTRY['tencent_finance'] = TencentFinanceAdapter
        ADAPTER_REGISTRY['xueqiu'] = XueqiuAdapter
        ADAPTER_REGISTRY['baidu_hot'] = BaiduHotAdapter
        ADAPTER_REGISTRY['weibo_hot'] = WeiboHotAdapter
        ADAPTER_REGISTRY['jrj_web'] = JinrongjieBaiduAdapter

        logger.info("预定义适配器注册完成")

    except ImportError as e:
        logger.warning(f"预定义适配器导入失败: {str(e)}")


# 自动注册预定义适配器
_register_predefined_adapters()
