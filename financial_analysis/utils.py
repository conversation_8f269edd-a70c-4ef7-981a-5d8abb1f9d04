"""
工具函数模块

提供项目中使用的各种工具函数，包括日志配置、数据处理、时间处理等。
"""

import os
import re
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from loguru import logger

from .config import settings


def setup_logging():
    """配置日志系统"""
    # 确保日志目录存在
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置loguru
    logger.remove()  # 移除默认处理器
    
    # 添加控制台输出
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        sink=settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        encoding="utf-8"
    )


def normalize_stock_symbol(symbol: str, exchange: Optional[str] = None) -> str:
    """
    标准化股票代码格式
    
    Args:
        symbol: 原始股票代码
        exchange: 交易所代码，如果未提供则尝试自动识别
        
    Returns:
        标准化后的股票代码
    """
    symbol = symbol.upper().strip()
    
    # 移除可能的前缀和后缀
    symbol = re.sub(r'^(SH|SZ|HK|US)\.?', '', symbol)
    symbol = re.sub(r'\.(SH|SZ|HK|SS|SZ)$', '', symbol)
    
    # 如果没有指定交易所，尝试根据代码格式自动识别
    if not exchange:
        if symbol.startswith('6'):
            exchange = 'SSE'  # 上海证券交易所
        elif symbol.startswith(('0', '3')):
            exchange = 'SZSE'  # 深圳证券交易所
        elif symbol.startswith('7') or len(symbol) == 5:
            exchange = 'HK'  # 香港交易所
        else:
            exchange = settings.default_exchange
    
    return symbol


def get_exchange_suffix(exchange: str) -> str:
    """
    获取交易所后缀
    
    Args:
        exchange: 交易所代码
        
    Returns:
        对应的后缀
    """
    exchange_map = {
        'SSE': '.SS',  # 上海证券交易所
        'SZSE': '.SZ',  # 深圳证券交易所
        'HK': '.HK',   # 香港交易所
        'NASDAQ': '',  # 纳斯达克
        'NYSE': '',    # 纽约证券交易所
    }
    return exchange_map.get(exchange.upper(), '')


def format_currency(amount: float, currency: str = 'CNY') -> str:
    """
    格式化货币显示
    
    Args:
        amount: 金额
        currency: 货币类型
        
    Returns:
        格式化后的货币字符串
    """
    if currency == 'CNY':
        if amount >= 100000000:  # 亿
            return f"¥{amount/100000000:.2f}亿"
        elif amount >= 10000:  # 万
            return f"¥{amount/10000:.2f}万"
        else:
            return f"¥{amount:.2f}"
    elif currency == 'USD':
        if amount >= 1000000000:  # 十亿
            return f"${amount/1000000000:.2f}B"
        elif amount >= 1000000:  # 百万
            return f"${amount/1000000:.2f}M"
        else:
            return f"${amount:.2f}"
    else:
        return f"{amount:.2f} {currency}"


def calculate_date_range(days: int) -> tuple[datetime, datetime]:
    """
    计算日期范围
    
    Args:
        days: 天数
        
    Returns:
        (开始日期, 结束日期)
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    return start_date, end_date


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的浮点数
    """
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的整数
    """
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default
