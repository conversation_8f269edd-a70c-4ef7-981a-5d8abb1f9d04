"""
分析引擎模块

整合股票数据和新闻信息，生成智能化的分析报告。
包括技术分析、基本面分析、新闻情感分析和综合投资建议。
"""

import json
from datetime import datetime
from typing import Optional, Dict, Any, List
import requests
from loguru import logger

from .models import StockInfo, StockPrice, TechnicalIndicators, NewsItem, AnalysisReport
from .stock_data import StockDataProvider
from .news_search import NewsSearcher
from .config import settings


class AnalysisEngine:
    """分析引擎类"""
    
    def __init__(self):
        """初始化分析引擎"""
        self.stock_data_provider = StockDataProvider()
        self.news_searcher = NewsSearcher()
        logger.info("分析引擎初始化完成")
    
    def generate_analysis_report(self, symbol: str, exchange: Optional[str] = None) -> Optional[AnalysisReport]:
        """
        生成完整的股票分析报告
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            
        Returns:
            分析报告对象，如果生成失败返回None
        """
        try:
            logger.info(f"开始生成分析报告: {symbol}")
            
            # 1. 获取股票基本信息
            stock_info = self.stock_data_provider.get_stock_info(symbol, exchange)
            if not stock_info:
                logger.error(f"无法获取股票信息: {symbol}")
                return None
            
            # 2. 获取价格数据
            prices = self.stock_data_provider.get_stock_prices(symbol, settings.analysis_days, exchange)
            if not prices:
                logger.error(f"无法获取价格数据: {symbol}")
                return None
            
            # 3. 获取技术指标
            technical_indicators = self.stock_data_provider.get_technical_indicators(symbol, exchange)
            if not technical_indicators:
                logger.error(f"无法获取技术指标: {symbol}")
                return None
            
            # 4. 搜索相关新闻
            news_items = self.news_searcher.search_stock_news(stock_info)
            
            # 5. 分析新闻情感
            news_sentiment_result = self.news_searcher.analyze_news_sentiment(news_items)
            
            # 6. 生成新闻摘要
            news_summary = self.news_searcher.generate_news_summary(news_items, stock_info)
            
            # 7. 计算价格变化
            current_price = prices[-1].close_price
            previous_price = prices[-2].close_price if len(prices) > 1 else current_price
            price_change = current_price - previous_price
            price_change_percent = (price_change / previous_price) * 100 if previous_price != 0 else 0
            
            # 8. 进行技术分析
            trend_analysis = self._analyze_trend(prices, technical_indicators)
            support_resistance = self._calculate_support_resistance(prices)
            
            # 9. 进行基本面分析
            fundamental_analysis = self._analyze_fundamentals(stock_info, prices)
            
            # 10. 生成综合评级和投资建议
            overall_rating, risk_level, investment_advice = self._generate_investment_advice(
                stock_info, prices, technical_indicators, news_sentiment_result
            )
            
            # 11. 生成AI分析报告
            ai_analysis = self._generate_ai_analysis(
                stock_info, prices, technical_indicators, news_items, news_sentiment_result
            )
            
            # 12. 构建分析报告
            report = AnalysisReport(
                symbol=symbol,
                stock_info=stock_info,
                analysis_date=datetime.now(),
                current_price=current_price,
                price_change=price_change,
                price_change_percent=price_change_percent,
                technical_indicators=technical_indicators,
                trend_analysis=trend_analysis,
                support_resistance=support_resistance,
                fundamental_analysis=fundamental_analysis,
                news_items=news_items,
                news_sentiment=news_sentiment_result['overall_sentiment'],
                news_summary=news_summary,
                overall_rating=overall_rating,
                risk_level=risk_level,
                investment_advice=investment_advice,
                ai_analysis=ai_analysis
            )
            
            logger.info(f"分析报告生成完成: {symbol}")
            return report
            
        except Exception as e:
            logger.error(f"生成分析报告失败 {symbol}: {str(e)}")
            return None
    
    def _analyze_trend(self, prices: List[StockPrice], indicators: TechnicalIndicators) -> str:
        """
        分析价格趋势
        
        Args:
            prices: 价格数据列表
            indicators: 技术指标
            
        Returns:
            趋势分析文本
        """
        try:
            if len(prices) < 5:
                return "数据不足，无法进行趋势分析"
            
            # 获取最近5天的收盘价
            recent_prices = [p.close_price for p in prices[-5:]]
            current_price = recent_prices[-1]
            
            # 分析短期趋势
            if len(recent_prices) >= 3:
                if recent_prices[-1] > recent_prices[-2] > recent_prices[-3]:
                    short_trend = "上升"
                elif recent_prices[-1] < recent_prices[-2] < recent_prices[-3]:
                    short_trend = "下降"
                else:
                    short_trend = "震荡"
            else:
                short_trend = "不明确"
            
            # 分析与移动平均线的关系
            ma_analysis = []
            if indicators.ma5 and current_price > indicators.ma5:
                ma_analysis.append("价格位于5日均线上方")
            elif indicators.ma5 and current_price < indicators.ma5:
                ma_analysis.append("价格位于5日均线下方")
            
            if indicators.ma20 and current_price > indicators.ma20:
                ma_analysis.append("价格位于20日均线上方")
            elif indicators.ma20 and current_price < indicators.ma20:
                ma_analysis.append("价格位于20日均线下方")
            
            # RSI分析
            rsi_analysis = ""
            if indicators.rsi:
                if indicators.rsi > 70:
                    rsi_analysis = "RSI显示超买状态"
                elif indicators.rsi < 30:
                    rsi_analysis = "RSI显示超卖状态"
                else:
                    rsi_analysis = "RSI处于正常区间"
            
            # 组合分析结果
            analysis_parts = [f"短期趋势：{short_trend}"]
            if ma_analysis:
                analysis_parts.extend(ma_analysis)
            if rsi_analysis:
                analysis_parts.append(rsi_analysis)
            
            return "；".join(analysis_parts)
            
        except Exception as e:
            logger.error(f"趋势分析失败: {str(e)}")
            return "趋势分析失败"

    def _calculate_support_resistance(self, prices: List[StockPrice]) -> Dict[str, float]:
        """
        计算支撑位和阻力位

        Args:
            prices: 价格数据列表

        Returns:
            包含支撑位和阻力位的字典
        """
        try:
            if len(prices) < 10:
                return {"support": 0.0, "resistance": 0.0}

            # 获取最近20天的价格数据
            recent_prices = prices[-20:] if len(prices) >= 20 else prices
            highs = [p.high_price for p in recent_prices]
            lows = [p.low_price for p in recent_prices]

            # 简单的支撑阻力计算
            # 支撑位：最近期间的最低价附近
            support = min(lows)

            # 阻力位：最近期间的最高价附近
            resistance = max(highs)

            return {
                "support": support,
                "resistance": resistance
            }

        except Exception as e:
            logger.error(f"支撑阻力计算失败: {str(e)}")
            return {"support": 0.0, "resistance": 0.0}

    def _analyze_fundamentals(self, stock_info: StockInfo, prices: List[StockPrice]) -> str:
        """
        基本面分析

        Args:
            stock_info: 股票基本信息
            prices: 价格数据列表

        Returns:
            基本面分析文本
        """
        try:
            analysis_parts = []

            # 市值分析
            if stock_info.market_cap:
                if stock_info.market_cap > 100000000000:  # 1000亿以上
                    analysis_parts.append("大盘股，市值规模较大，相对稳定")
                elif stock_info.market_cap > 10000000000:  # 100亿以上
                    analysis_parts.append("中盘股，具有一定成长性")
                else:
                    analysis_parts.append("小盘股，成长潜力较大但风险也较高")

            # 行业分析
            if stock_info.sector:
                analysis_parts.append(f"所属{stock_info.sector}行业")

            # 价格波动性分析
            if len(prices) >= 10:
                recent_prices = [p.close_price for p in prices[-10:]]
                price_std = self._calculate_std(recent_prices)
                avg_price = sum(recent_prices) / len(recent_prices)
                volatility = (price_std / avg_price) * 100 if avg_price != 0 else 0

                if volatility > 5:
                    analysis_parts.append("价格波动较大")
                elif volatility > 2:
                    analysis_parts.append("价格波动适中")
                else:
                    analysis_parts.append("价格相对稳定")

            return "；".join(analysis_parts) if analysis_parts else "基本面信息有限"

        except Exception as e:
            logger.error(f"基本面分析失败: {str(e)}")
            return "基本面分析失败"

    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差"""
        if not values:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5

    def _generate_investment_advice(
        self,
        stock_info: StockInfo,
        prices: List[StockPrice],
        indicators: TechnicalIndicators,
        news_sentiment: Dict[str, Any]
    ) -> tuple[str, str, str]:
        """
        生成投资建议

        Args:
            stock_info: 股票基本信息
            prices: 价格数据
            indicators: 技术指标
            news_sentiment: 新闻情感分析结果

        Returns:
            (综合评级, 风险等级, 投资建议)
        """
        try:
            score = 0  # 综合评分
            risk_factors = []

            # 技术面评分
            if indicators.rsi:
                if 30 <= indicators.rsi <= 70:
                    score += 1  # RSI正常
                elif indicators.rsi < 30:
                    score += 2  # 超卖，可能反弹
                else:
                    score -= 1  # 超买，可能回调

            # 趋势评分
            current_price = prices[-1].close_price
            if indicators.ma5 and current_price > indicators.ma5:
                score += 1
            if indicators.ma20 and current_price > indicators.ma20:
                score += 1

            # 新闻情感评分
            sentiment_score = news_sentiment.get('sentiment_score', 0)
            if sentiment_score > 0.2:
                score += 2
            elif sentiment_score < -0.2:
                score -= 2

            # 波动性风险评估
            if len(prices) >= 10:
                recent_prices = [p.close_price for p in prices[-10:]]
                volatility = self._calculate_std(recent_prices) / (sum(recent_prices) / len(recent_prices)) * 100

                if volatility > 5:
                    risk_factors.append("高波动性")
                elif volatility > 2:
                    risk_factors.append("中等波动性")

            # 市值风险评估
            if stock_info.market_cap and stock_info.market_cap < 10000000000:
                risk_factors.append("小盘股风险")

            # 确定评级
            if score >= 3:
                overall_rating = "买入"
            elif score >= 1:
                overall_rating = "持有"
            else:
                overall_rating = "卖出"

            # 确定风险等级
            if len(risk_factors) >= 2:
                risk_level = "高"
            elif len(risk_factors) == 1:
                risk_level = "中"
            else:
                risk_level = "低"

            # 生成投资建议
            advice_parts = [f"综合评分: {score}分"]
            if risk_factors:
                advice_parts.append(f"风险因素: {', '.join(risk_factors)}")

            if overall_rating == "买入":
                advice_parts.append("技术面和基本面均显示积极信号，建议适量买入")
            elif overall_rating == "持有":
                advice_parts.append("当前状态相对稳定，建议继续持有观察")
            else:
                advice_parts.append("存在一定风险，建议谨慎或考虑减仓")

            investment_advice = "；".join(advice_parts)

            return overall_rating, risk_level, investment_advice

        except Exception as e:
            logger.error(f"生成投资建议失败: {str(e)}")
            return "持有", "中", "分析过程中出现错误，建议谨慎投资"

    def _generate_ai_analysis(
        self,
        stock_info: StockInfo,
        prices: List[StockPrice],
        indicators: TechnicalIndicators,
        news_items: List[NewsItem],
        news_sentiment: Dict[str, Any]
    ) -> str:
        """
        生成AI分析报告

        Args:
            stock_info: 股票基本信息
            prices: 价格数据
            indicators: 技术指标
            news_items: 新闻条目
            news_sentiment: 新闻情感分析

        Returns:
            AI生成的详细分析报告
        """
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，返回简化分析")
                return self._generate_simple_analysis(stock_info, prices, indicators, news_sentiment)

            # 准备分析数据
            analysis_data = self._prepare_analysis_data(stock_info, prices, indicators, news_items, news_sentiment)

            # 调用Windmill接口生成AI分析
            ai_analysis = self._call_windmill_analysis(analysis_data)

            return ai_analysis or self._generate_simple_analysis(stock_info, prices, indicators, news_sentiment)

        except Exception as e:
            logger.error(f"生成AI分析失败: {str(e)}")
            return self._generate_simple_analysis(stock_info, prices, indicators, news_sentiment)

    def _prepare_analysis_data(
        self,
        stock_info: StockInfo,
        prices: List[StockPrice],
        indicators: TechnicalIndicators,
        news_items: List[NewsItem],
        news_sentiment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """准备分析数据"""
        # 价格统计
        current_price = prices[-1].close_price
        price_change = current_price - prices[-2].close_price if len(prices) > 1 else 0
        price_change_percent = (price_change / prices[-2].close_price) * 100 if len(prices) > 1 and prices[-2].close_price != 0 else 0

        # 最近价格趋势
        recent_prices = [p.close_price for p in prices[-5:]]

        # 新闻标题
        news_titles = [item.title for item in news_items[:5]]

        return {
            "stock_info": {
                "symbol": stock_info.symbol,
                "name": stock_info.name,
                "exchange": stock_info.exchange,
                "sector": stock_info.sector,
                "market_cap": stock_info.market_cap
            },
            "price_data": {
                "current_price": current_price,
                "price_change": price_change,
                "price_change_percent": price_change_percent,
                "recent_prices": recent_prices
            },
            "technical_indicators": {
                "ma5": indicators.ma5,
                "ma20": indicators.ma20,
                "rsi": indicators.rsi,
                "macd": indicators.macd
            },
            "news_analysis": {
                "overall_sentiment": news_sentiment.get('overall_sentiment'),
                "sentiment_score": news_sentiment.get('sentiment_score'),
                "news_count": len(news_items),
                "recent_news_titles": news_titles
            }
        }

    def _call_windmill_analysis(self, analysis_data: Dict[str, Any]) -> Optional[str]:
        """调用Windmill生成结构化文本接口生成AI分析"""
        try:
            url = f"{settings.windmill_base_url}/api/w/{settings.windmill_workspace}/jobs/run/p/f/{settings.windmill_folder}/{settings.windmill_script}"

            headers = {
                'Authorization': f'Bearer {settings.windmill_token}',
                'Content-Type': 'application/json'
            }

            # 定义响应结构
            response_schema = {
                "type": "object",
                "properties": {
                    "analysis": {"type": "string"}
                },
                "required": ["analysis"]
            }

            # 构造提示词
            prompt = f"""
            请基于以下数据对股票 {analysis_data['stock_info']['name']} ({analysis_data['stock_info']['symbol']}) 进行专业的投资分析：

            基本信息：
            - 交易所：{analysis_data['stock_info']['exchange']}
            - 行业：{analysis_data['stock_info']['sector']}
            - 市值：{analysis_data['stock_info']['market_cap']}

            价格数据：
            - 当前价格：{analysis_data['price_data']['current_price']}
            - 价格变化：{analysis_data['price_data']['price_change']} ({analysis_data['price_data']['price_change_percent']:.2f}%)
            - 最近价格走势：{analysis_data['price_data']['recent_prices']}

            技术指标：
            - 5日均线：{analysis_data['technical_indicators']['ma5']}
            - 20日均线：{analysis_data['technical_indicators']['ma20']}
            - RSI：{analysis_data['technical_indicators']['rsi']}
            - MACD：{analysis_data['technical_indicators']['macd']}

            新闻分析：
            - 整体情感：{analysis_data['news_analysis']['overall_sentiment']}
            - 情感得分：{analysis_data['news_analysis']['sentiment_score']}
            - 新闻数量：{analysis_data['news_analysis']['news_count']}
            - 主要新闻：{', '.join(analysis_data['news_analysis']['recent_news_titles'])}

            请提供：
            1. 技术面分析
            2. 基本面分析
            3. 市场情绪分析
            4. 风险评估
            5. 投资建议

            要求：
            - 分析要专业、客观
            - 长度控制在500字以内
            - 使用中文
            - 包含具体的数据支撑
            """

            payload = {
                "prompt": prompt,
                "system_instruction": "你是一位专业的金融分析师，具有丰富的股票分析经验。请基于提供的数据进行客观、专业的分析。",
                "responseSchema": response_schema
            }

            logger.debug("调用Windmill API生成AI分析")
            response = requests.post(url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            analysis = result.get('analysis', '')

            logger.info("AI分析生成成功")
            return analysis

        except Exception as e:
            logger.error(f"Windmill AI分析失败: {str(e)}")
            return None

    def _generate_simple_analysis(
        self,
        stock_info: StockInfo,
        prices: List[StockPrice],
        indicators: TechnicalIndicators,
        news_sentiment: Dict[str, Any]
    ) -> str:
        """生成简化分析报告"""
        current_price = prices[-1].close_price
        price_change = current_price - prices[-2].close_price if len(prices) > 1 else 0
        price_change_percent = (price_change / prices[-2].close_price) * 100 if len(prices) > 1 and prices[-2].close_price != 0 else 0

        analysis_parts = [
            f"【技术分析】{stock_info.name} 当前价格 {current_price:.2f}，",
            f"较前一交易日{'上涨' if price_change > 0 else '下跌'} {abs(price_change):.2f} ({abs(price_change_percent):.2f}%)。"
        ]

        if indicators.rsi:
            if indicators.rsi > 70:
                analysis_parts.append("RSI显示超买状态，短期可能面临回调压力。")
            elif indicators.rsi < 30:
                analysis_parts.append("RSI显示超卖状态，可能存在反弹机会。")
            else:
                analysis_parts.append("RSI处于正常区间，技术面相对健康。")

        sentiment = news_sentiment.get('overall_sentiment', 'neutral')
        if sentiment == 'positive':
            analysis_parts.append("【市场情绪】新闻面偏向正面，市场情绪较为乐观。")
        elif sentiment == 'negative':
            analysis_parts.append("【市场情绪】新闻面偏向负面，需要关注潜在风险。")
        else:
            analysis_parts.append("【市场情绪】新闻面相对中性，市场情绪稳定。")

        analysis_parts.append("【投资建议】以上分析仅供参考，投资有风险，决策需谨慎。")

        return "".join(analysis_parts)
