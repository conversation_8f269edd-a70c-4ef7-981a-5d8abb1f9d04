"""
新闻搜索模块

通过Gemini大语言模型和Windmill部署的接口搜索股票相关新闻，
并进行情感分析和内容摘要。
"""

import json
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import requests
from loguru import logger

from .models import NewsItem, StockInfo
from .config import settings


class NewsSearcher:
    """新闻搜索器类"""
    
    def __init__(self):
        """初始化新闻搜索器"""
        self._cache = {}  # 简单的内存缓存
        self._cache_timeout = settings.data_cache_duration
        logger.info("新闻搜索器初始化完成")
    
    def search_stock_news(self, stock_info: StockInfo, days: int = None) -> List[NewsItem]:
        """
        搜索股票相关新闻
        
        Args:
            stock_info: 股票基本信息
            days: 搜索天数，默认使用配置中的值
            
        Returns:
            新闻条目列表
        """
        try:
            days = days or settings.news_search_days
            cache_key = f"news_{stock_info.symbol}_{days}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取新闻数据: {stock_info.symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"搜索股票新闻: {stock_info.name} ({stock_info.symbol}), 天数: {days}")
            
            # 构造搜索查询
            search_query = self._build_search_query(stock_info, days)
            
            # 调用Windmill接口进行搜索
            news_items = self._search_via_windmill(search_query, stock_info)
            
            # 缓存结果
            if news_items:
                self._cache[cache_key] = {
                    'data': news_items,
                    'timestamp': time.time()
                }
            
            return news_items
            
        except Exception as e:
            logger.error(f"搜索股票新闻失败 {stock_info.symbol}: {str(e)}")
            return []
    
    def analyze_news_sentiment(self, news_items: List[NewsItem]) -> Dict[str, Any]:
        """
        分析新闻情感倾向
        
        Args:
            news_items: 新闻条目列表
            
        Returns:
            情感分析结果，包含整体情感、正面/负面新闻数量等
        """
        try:
            if not news_items:
                return {
                    'overall_sentiment': 'neutral',
                    'positive_count': 0,
                    'negative_count': 0,
                    'neutral_count': 0,
                    'sentiment_score': 0.0
                }
            
            logger.info(f"分析 {len(news_items)} 条新闻的情感倾向")
            
            # 统计各种情感的新闻数量
            positive_count = sum(1 for item in news_items if item.sentiment == 'positive')
            negative_count = sum(1 for item in news_items if item.sentiment == 'negative')
            neutral_count = sum(1 for item in news_items if item.sentiment == 'neutral')
            
            # 计算情感得分 (正面+1, 负面-1, 中性0)
            sentiment_score = (positive_count - negative_count) / len(news_items)
            
            # 确定整体情感
            if sentiment_score > 0.2:
                overall_sentiment = 'positive'
            elif sentiment_score < -0.2:
                overall_sentiment = 'negative'
            else:
                overall_sentiment = 'neutral'
            
            return {
                'overall_sentiment': overall_sentiment,
                'positive_count': positive_count,
                'negative_count': negative_count,
                'neutral_count': neutral_count,
                'sentiment_score': sentiment_score
            }
            
        except Exception as e:
            logger.error(f"分析新闻情感失败: {str(e)}")
            return {
                'overall_sentiment': 'neutral',
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'sentiment_score': 0.0
            }
    
    def generate_news_summary(self, news_items: List[NewsItem], stock_info: StockInfo) -> str:
        """
        生成新闻摘要
        
        Args:
            news_items: 新闻条目列表
            stock_info: 股票基本信息
            
        Returns:
            新闻摘要文本
        """
        try:
            if not news_items:
                return f"未找到关于 {stock_info.name} ({stock_info.symbol}) 的相关新闻。"
            
            logger.info(f"生成新闻摘要: {stock_info.symbol}")
            
            # 准备新闻内容用于摘要
            news_content = []
            for item in news_items[:10]:  # 最多使用前10条新闻
                content = f"标题: {item.title}"
                if item.content:
                    content += f"\n内容: {item.content[:200]}..."  # 限制内容长度
                content += f"\n来源: {item.source}\n发布时间: {item.publish_time.strftime('%Y-%m-%d %H:%M')}\n"
                news_content.append(content)
            
            # 调用Windmill接口生成摘要
            summary = self._generate_summary_via_windmill(news_content, stock_info)
            
            return summary or f"关于 {stock_info.name} 的新闻摘要生成失败。"
            
        except Exception as e:
            logger.error(f"生成新闻摘要失败: {str(e)}")
            return f"关于 {stock_info.name} 的新闻摘要生成失败。"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False

        cache_time = self._cache[cache_key]['timestamp']
        return time.time() - cache_time < self._cache_timeout

    def _build_search_query(self, stock_info: StockInfo, days: int) -> str:
        """构造搜索查询"""
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构造查询字符串
        query_parts = [
            stock_info.name,
            stock_info.symbol,
        ]

        # 添加行业相关关键词
        if stock_info.sector:
            query_parts.append(stock_info.sector)

        # 添加时间限制
        date_range = f"{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}"

        query = f"搜索关于 {' '.join(query_parts)} 的最新新闻，时间范围：{date_range}"

        logger.debug(f"构造的搜索查询: {query}")
        return query

    def _search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
        """通过Windmill接口搜索新闻"""
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，使用模拟数据")
                return self._generate_mock_news(stock_info)

            # 构造Windmill生成文本接口请求
            url = f"{settings.windmill_base_url}/api/w/{settings.windmill_workspace}/jobs/run/p/f/{settings.windmill_folder}/{settings.windmill_script}"

            headers = {
                'Authorization': f'Bearer {settings.windmill_token}',
                'Content-Type': 'application/json'
            }

            # 定义响应结构
            response_schema = {
                "type": "object",
                "properties": {
                    "news_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "content": {"type": "string"},
                                "source": {"type": "string"},
                                "publish_time": {"type": "string"},
                                "url": {"type": "string"},
                                "sentiment": {"type": "string", "enum": ["positive", "negative", "neutral"]}
                            },
                            "required": ["title", "source", "publish_time", "sentiment"]
                        }
                    }
                }
            }

            payload = {
                "prompt": search_query,
                "system_instruction": "你是一个专业的金融新闻搜索助手。请搜索相关的股票新闻，并分析每条新闻的情感倾向（positive/negative/neutral）。",
                "responseSchema": response_schema
            }

            logger.debug(f"调用Windmill API: {url}")
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            news_data = result.get('news_items', [])

            # 转换为NewsItem对象
            news_items = []
            for item in news_data:
                try:
                    news_item = NewsItem(
                        title=item['title'],
                        content=item.get('content'),
                        source=item['source'],
                        publish_time=datetime.fromisoformat(item['publish_time'].replace('Z', '+00:00')),
                        url=item.get('url'),
                        sentiment=item['sentiment']
                    )
                    news_items.append(news_item)
                except Exception as e:
                    logger.warning(f"解析新闻条目失败: {str(e)}")
                    continue

            logger.info(f"通过Windmill获取到 {len(news_items)} 条新闻")
            return news_items

        except Exception as e:
            logger.error(f"Windmill新闻搜索失败: {str(e)}")
            # 降级到模拟数据
            return self._generate_mock_news(stock_info)

    def _generate_summary_via_windmill(self, news_content: List[str], stock_info: StockInfo) -> Optional[str]:
        """通过Windmill生成文本接口生成新闻摘要"""
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，返回简单摘要")
                return f"关于 {stock_info.name} 共找到 {len(news_content)} 条相关新闻。"

            # 构造Windmill生成文本接口请求
            url = f"{settings.windmill_base_url}/api/w/{settings.windmill_workspace}/jobs/run/p/f/{settings.windmill_folder}/{settings.windmill_script}"

            headers = {
                'Authorization': f'Bearer {settings.windmill_token}',
                'Content-Type': 'application/json'
            }

            # 定义响应结构
            response_schema = {
                "type": "object",
                "properties": {
                    "summary": {"type": "string"}
                },
                "required": ["summary"]
            }

            # 准备新闻内容
            news_text = "\n\n".join(news_content)

            prompt = f"""
            请为以下关于 {stock_info.name} ({stock_info.symbol}) 的新闻内容生成一个简洁的摘要：

            {news_text}

            要求：
            1. 摘要应该突出重要的市场信息和趋势
            2. 长度控制在200字以内
            3. 使用中文
            4. 客观中性的语调
            """

            payload = {
                "prompt": prompt,
                "system_instruction": "你是一个专业的金融分析师，擅长总结和分析金融新闻。",
                "responseSchema": response_schema
            }

            logger.debug("调用Windmill API生成新闻摘要")
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            summary = result.get('summary', '')

            logger.info("新闻摘要生成成功")
            return summary

        except Exception as e:
            logger.error(f"Windmill摘要生成失败: {str(e)}")
            return None

    def _generate_mock_news(self, stock_info: StockInfo) -> List[NewsItem]:
        """生成模拟新闻数据（用于测试和降级）"""
        logger.info(f"生成模拟新闻数据: {stock_info.symbol}")

        mock_news = [
            NewsItem(
                title=f"{stock_info.name}发布最新财报，业绩超预期",
                content=f"{stock_info.name}公司发布了最新季度财报，营收和利润均超出市场预期。",
                source="财经新闻网",
                publish_time=datetime.now() - timedelta(hours=2),
                url="https://example.com/news1",
                sentiment="positive"
            ),
            NewsItem(
                title=f"市场分析师看好{stock_info.name}未来发展前景",
                content=f"多位分析师表示，{stock_info.name}在行业中的竞争优势明显。",
                source="投资者报",
                publish_time=datetime.now() - timedelta(hours=6),
                url="https://example.com/news2",
                sentiment="positive"
            ),
            NewsItem(
                title=f"{stock_info.name}面临行业监管政策调整压力",
                content=f"新的行业监管政策可能对{stock_info.name}的业务产生一定影响。",
                source="监管快讯",
                publish_time=datetime.now() - timedelta(hours=12),
                url="https://example.com/news3",
                sentiment="negative"
            )
        ]

        return mock_news
